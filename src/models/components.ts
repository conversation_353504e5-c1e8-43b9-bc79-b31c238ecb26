import type { ReactNode } from "react";

// ========== TIPOS DE COMPONENTES DE VISTA ==========
export interface ViewProps {
  isOpen: boolean;
  onClose: () => void;
  onStart: (mode: "player_vs_ia" | "ia_vs_player") => Promise<void>;
}

// ========== COMPONENTES DE REGLAS ==========
export interface RulePage {
  id: number;
  title: string;
  image?: string;
}

export interface RulesViewData {
  title: string;
  pages: RulePage[];
}

// ========== COMPONENTES DE JUEGO ==========
export interface PlayViewProps {
  // Props específicas de PlayView
}

export interface MainViewProps {
  // Props específicas de MainView
}

export interface CluesViewProps extends ViewProps {
  // Props específicas de CluesView
}

export interface LivesViewProps extends ViewProps {
  // Props específicas de LivesView
}

// ========== COMPONENTES DE HEADER ==========
export interface HeaderProps {
  title?: string;
  showBackButton?: boolean;
  showHomeButton?: boolean;
  showSoundButton?: boolean;
  onBack?: () => void;
  onHome?: () => void;
  onSound?: () => void;
}

// ========== COMPONENTES DE ICONOS ==========
export interface IconProps {
  size?: number;
  color?: string;
  className?: string;
  onClick?: () => void;
}

export interface IconGoBackProps extends IconProps {
  // Props específicas del icono de volver
}

export interface IconHomeProps extends IconProps {
  // Props específicas del icono de home
}

export interface IconMenuProps extends IconProps {
  // Props específicas del icono de menú
}

export interface IconSoundOnProps extends IconProps {
  // Props específicas del icono de sonido
}

// ========== COMPONENTES DE PANTALLA DE BIENVENIDA ==========
export interface WelcomeScreenProps {
  isVisible: boolean;
  onComplete: () => void;
}

// ========== COMPONENTES DE BANNER DE COOKIES ==========
export interface CookieConsentBannerProps {
  isVisible: boolean;
  onAccept: () => void;
  onReject: () => void;
}

// ========== HOOKS DE COMPONENTES ==========
export interface UseSpeechCoordinatorReturn {
  // Estado
  state: any;
  isSpeaking: boolean;
  queueLength: number;
  currentSpeech: string | null;

  // Métodos principales
  speak: (text: string, type?: any) => Promise<void>;
  speakError: (text: string) => Promise<void>;
  speakValidation: (text: string) => Promise<void>;
  speakGameResponse: (text: string) => Promise<void>;
  speakGameQuestion: (text: string) => Promise<void>;
  speakWelcome: (text: string) => Promise<void>;
  speakHint: (text: string) => Promise<void>;
  speakInfo: (text: string) => Promise<void>;

  // Control
  interrupt: () => void;
  clearQueue: () => void;
  stopAll: () => void;

  // Canales específicos
  speakWeb: (text: string, type?: any) => Promise<void>;

  // Utilidades
  speakWithCallback: (text: string, type: any, onComplete: () => void) => Promise<void>;
}

// ========== PROPS COMUNES ==========
export interface BaseComponentProps {
  children?: ReactNode;
  className?: string;
  id?: string;
}

export interface ClickableProps {
  onClick?: () => void;
  disabled?: boolean;
}

export interface LoadingProps {
  isLoading?: boolean;
  loadingText?: string;
}

export interface ErrorProps {
  error?: string | null;
  onErrorClear?: () => void;
}

// ========== TIPOS DE BOTONES ==========
export interface ButtonProps extends BaseComponentProps, ClickableProps, LoadingProps {
  variant?: "primary" | "secondary" | "danger" | "success";
  size?: "small" | "medium" | "large";
  type?: "button" | "submit" | "reset";
}

// ========== TIPOS DE MODALES ==========
export interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  showCloseButton?: boolean;
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
}

// ========== TIPOS DE FORMULARIOS ==========
export interface FormProps extends BaseComponentProps {
  onSubmit: (data: any) => void;
  onCancel?: () => void;
  isSubmitting?: boolean;
  validationErrors?: Record<string, string>;
}

export interface InputProps extends BaseComponentProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  type?: "text" | "email" | "password" | "number";
  required?: boolean;
  disabled?: boolean;
  error?: string;
}
