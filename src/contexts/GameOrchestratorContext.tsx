import {
  createContext,
  useContext,
  useCallback,
  type ReactNode,
  useState,
} from "react";
import { useEnygmaGame } from "./EnygmaGameContext";
import { useSpeechInput, useSpeechOutput } from "./SpeechProvider";
import { useGameSpeech } from "../hooks/useSpeechCoordinator";

/**
 * ========================================================================
 * GameOrchestratorProvider - Contexto de Orquestación del Juego Enygma
 *
 * Gestiona:
 * -  Coordinación entre servicios (speech, AI, game)
 * - Flujo de aplicación y estados de UI
 * - Manejo de errores y recuperación
 * - Anuncios y mensajes del sistema
 * - Inicialización completa de la aplicación
 * =======================================================================
 */

export type GameFlowState =
  | "idle"
  | "initializing"
  | "waiting_for_user_choice"
  | "game_active"
  | "processing_response"
  | "game_finished"
  | "showing_results"
  | "error";

export interface GameOrchestratorContextProps {
  // Estado de aplicación
  flowState: GameFlowState;
  isProcessing: boolean;
  error: string | null;
  isReady: boolean;
  setupProgress: number;
  isSilentMode: boolean;

  // Control de flujo
  initializeApp: () => Promise<void>;
  startGameFlow: (mode: "ia_vs_player") => Promise<void>;
}

const GameOrchestratorContext = createContext<
  GameOrchestratorContextProps | undefined
>(undefined);

export const useGameOrchestrator = () => {
  const context = useContext(GameOrchestratorContext);
  if (!context) {
    throw new Error(
      "useGameOrchestrator must be used within GameOrchestratorProvider"
    );
  }
  return context;
};

export const GameOrchestratorProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  // Context dependencies
  const game = useEnygmaGame();
  const speechInput = useSpeechInput();
  const speechOutput = useSpeechOutput();
  const gameSpeech = useGameSpeech();

  // Local state
  const [flowState, setFlowState] = useState<GameFlowState>("idle");
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [setupProgress, setSetupProgress] = useState<number>(0);

  // Computed state
  const isReady = flowState !== "idle" && flowState !== "initializing";

  // Indicador de modo silencioso (sin voz)
  const [isSilentMode, setIsSilentMode] = useState<boolean>(false);

  // Initialize the entire app
  const initializeApp = useCallback(async (): Promise<void> => {
    setFlowState("initializing");
    setSetupProgress(0);

    try {
      console.log("ℹ️ [orchestrator] 🚀 Initializing Enygma app");

      // Step 1: Configure speech output (25%) - Con manejo de errores mejorado
      try {
        console.log("ℹ️ [orchestrator] 🔧 Configuring speech output");
        const speechConfigured = await speechOutput.configure("female");
        if (!speechConfigured) {
          console.warn(
            "⚠️ [orchestrator] No se pudo configurar la voz, activando modo silencioso"
          );
          setIsSilentMode(true);
        }
        setSetupProgress(25);
      } catch (speechConfigError) {
        console.warn(
          "⚠️ [orchestrator] Error configurando voz, activando modo silencioso:",
          speechConfigError
        );
        setIsSilentMode(true);
        setSetupProgress(25);
      }

      // Step 2: Start speech input (50%) - Con manejo de errores mejorado
      try {
        console.log("ℹ️ [orchestrator] 🎤 Starting speech input");
        setSetupProgress(50);
      } catch (speechInputError) {
        console.warn(
          "⚠️ [orchestrator] Error iniciando entrada de voz:",
          speechInputError
        );
        // Continuar sin entrada de voz - no es crítico
        setSetupProgress(50);
      }

      // Step 3: Test AI connectivity (75%)
      console.log("ℹ️ [orchestrator] 🤖 Testing AI connectivity");
      // Could add a quick AI health check here
      setSetupProgress(75);

      // Step 4: Welcome message (100%) - Con manejo de errores mejorado
      try {
        console.log("ℹ️ [orchestrator] 👋 Playing welcome message");
        await gameSpeech.speakWelcome(
          "El velo del misterio se alza. Bienvenido a Enygma. ¿Estás listo para el desafío?"
        );
        console.log("✅ [orchestrator] ✅ Welcome message played");
      } catch (welcomeError) {
        console.warn(
          "⚠️ [orchestrator] Error reproduciendo mensaje de bienvenida:",
          welcomeError
        );
        // Continuar sin mensaje de bienvenida - no es crítico
      }

      // Step 5: Start background music - Con manejo de errores mejorado
      try {
        await speechOutput.playBackgroundMusic();
        console.log("✅ [orchestrator] 🎵 Background music started");
      } catch (musicError) {
        console.warn(
          "⚠️ [orchestrator] No se pudo iniciar música de fondo:",
          musicError
        );
        // No es crítico, la música se puede iniciar después con interacción del usuario
      }

      // Completar inicialización independientemente de errores no críticos
      setSetupProgress(100);
      setFlowState("waiting_for_user_choice");
      console.log("✅ [orchestrator] ✅ App initialized successfully");
    } catch (error) {
      console.error(
        "❌ [orchestrator] Error crítico en inicialización:",
        error
      );

      // Aún así, intentar completar la inicialización para que la app funcione
      setSetupProgress(100);
      setFlowState("waiting_for_user_choice");

      // Registrar el error pero no cambiar a estado de error
      // para permitir que la aplicación funcione sin voz
      setError(
        error instanceof Error ? error.message : "Unknown initialization error"
      );
    }
  }, [gameSpeech, speechInput]);

  // Announce game start
  const announceGameStart = useCallback(
    async (mode: "player_vs_ia" | "ia_vs_player"): Promise<void> => {
      try {
        console.log(`ℹ️ [orchestrator] 🎮 Announcing game start: ${mode}`);

        // Speak the announcement (game context handles message storage)
        try {
          // await gameSpeech.speakWelcome(announcement);
          console.log("✅ [orchestrator] ✅ Game start announced");
        } catch (speechError) {
          console.warn(
            "⚠️ [orchestrator] Error anunciando inicio del juego:",
            speechError
          );
          // Continuar sin anuncio de voz - no es crítico
        }
      } catch (error) {
        console.error(
          "❌ [orchestrator] Error crítico anunciando inicio del juego:",
          error
        );
        // No lanzar el error para permitir que el juego continúe sin voz
      }
    },
    [gameSpeech]
  );

  // Start the game flow
  const startGameFlow = useCallback(
    async (mode: "ia_vs_player"): Promise<void> => {
      setFlowState("initializing");
      setIsProcessing(true);

      try {
        // Start the game session
        await game.startNewGame(mode);

        // Announce game start
        await announceGameStart(mode);

        setFlowState("game_active");
      } catch (error) {
        setError(
          error instanceof Error ? error.message : "Failed to start game"
        );
        setFlowState("error");
      } finally {
        setIsProcessing(false);
      }
    },
    [game, announceGameStart]
  );

  return (
    <GameOrchestratorContext.Provider
      value={{
        flowState,
        isProcessing,
        error,
        initializeApp,
        startGameFlow,
        isReady,
        setupProgress,
        isSilentMode,
      }}
    >
      {children}
    </GameOrchestratorContext.Provider>
  );
};
