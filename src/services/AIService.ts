/**
 * ========================================================================
 * AI SERVICE - REFACTORIZADO
 * ========================================================================
 *
 * Servicio principal de IA simplificado que utiliza todos los módulos
 * especializados para una mejor separación de responsabilidades
 * ========================================================================
 */

import type { AIResponse } from "../models";
import type { AIServiceStats, AISessionConfig, GameClue, GenerateOptions } from "./ai/models/AIModels";

// Importar módulos especializados
import { API_CONFIG, validateConfig } from "./ai/config/APIConfig";
import { responseParser } from './ai/models/ResponseParser';
import { cluesStorage } from './ai/storage/CluesStorage';
import { characterStorage } from './ai/storage/CharacterStorage';
import { payloadBuilder } from './ai/utils/PayloadBuilder';
import { requestHelper } from './ai/utils/RequestHelper';
import { speechIntegration } from './ai/speech/SpeechIntegration';
import type { GameMode } from "../contexts/EnygmaGameContext";

// ========== CLASE PRINCIPAL ==========
export class AIService {
  private static instance: AIService;
  private serviceName = "aiService";

  // Estado de sesión
  private sessionConfig: AISessionConfig = {
    sessionId: null,
    mode: null,
    character: null,
    startTime: null,
    lastActivity: null,
    messageCount: 0,
    cluesGenerated: []
  };

  // Estadísticas
  private stats: AIServiceStats = {
    sessionsCreated: 0,
    requestsCount: 0,
    responsesParsed: 0,
    cluesGenerated: 0,
    charactersGenerated: 0,
    errorsCount: 0,
    lastActivity: new Date()
  };

  private constructor() {
    this.initializeService();
  }

  public static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  // ========== INICIALIZACIÓN ==========

  private initializeService(): void {
    console.log(`ℹ️ [${this.serviceName}] 🤖 AIService refactorizado inicializado`);

    // Validar configuración
    const configValidation = validateConfig();
    if (!configValidation.isValid) {
      console.error(`❌ [${this.serviceName}] Configuración inválida:`, configValidation.errors);
    } else if (configValidation.warnings.length > 0) {
      console.warn(`⚠️ [${this.serviceName}] Advertencias de configuración:`, configValidation.warnings);
    }

    // Configurar speech por defecto
    speechIntegration.configure({
      enabled: true,
      autoNarrate: true,
      responseNarration: true,
      characterAnnouncements: true,
      welcomeMessages: true
    });
  }

  // ========== GETTERS ==========

  public getSessionId(): string | null {
    return this.sessionConfig.sessionId;
  }

  public getSessionConfig(): AISessionConfig {
    return { ...this.sessionConfig };
  }

  public getStats(): AIServiceStats {
    return { ...this.stats };
  }

  public getStoredCharacter(): string | null {
    return characterStorage.getCurrentCharacter();
  }

  public getApiConfig() {
    return { ...API_CONFIG };
  }

  // ========== GESTIÓN DE PISTAS ==========

  public getAllClues(): GameClue[] {
    return cluesStorage.getAll();
  }

  public getSessionClues(): GameClue[] {
    if (!this.sessionConfig.sessionId) return [];
    return cluesStorage.getBySession(this.sessionConfig.sessionId);
  }

  public clearAllClues(): void {
    cluesStorage.clearAll();
  }

  public getCluesCount(): number {
    return cluesStorage.getCount();
  }

  public getRecentClues(limit: number = 5): GameClue[] {
    return cluesStorage.getRecent(limit);
  }

  // ========== CONFIGURACIÓN DE SPEECH ==========

  public enableAutoSpeech(): void {
    speechIntegration.configure({ autoNarrate: true });
    console.log(`ℹ️ [${this.serviceName}] 🔊 Auto-speech habilitado`);
  }

  public disableAutoSpeech(): void {
    speechIntegration.configure({ autoNarrate: false });
    console.log(`ℹ️ [${this.serviceName}] 🔇 Auto-speech deshabilitado`);
  }

  public isAutoSpeechEnabled(): boolean {
    return speechIntegration.getConfig().autoNarrate;
  }

  // ========== MÉTODOS PRINCIPALES ==========

  /**
   * Genera respuesta de IA con manejo completo
   */
  public async generateResponse(
    query: string,
    mode: GameMode,
    character?: string
  ): Promise<AIResponse> {
    console.log(`ℹ️ [${this.serviceName}] Generando respuesta para modo ${mode}`, {
      query: query.substring(0, 100),
      sessionId: this.sessionConfig.sessionId,
    });

    try {
      this.updateSessionActivity();
      this.stats.requestsCount++;

      // Construir opciones
      const options: GenerateOptions = {
        query,
        mode,
        character,
        sessionId: this.sessionConfig.sessionId || undefined
      };

      // Construir payload
      const payload = payloadBuilder.buildGeneratePayload(options);

      // Validar payload
      const validation = payloadBuilder.validatePayload(payload);
      if (!validation.isValid) {
        throw new Error(`Payload inválido: ${validation.errors.join(', ')}`);
      }

      // Realizar petición
      const result = await requestHelper.makeRequest<any>("generate", payload);

      if (!result.success) {
        throw new Error(result.error || "Error en la respuesta de la IA");
      }

      const data = result.data;

      // Actualizar sessionId si es una nueva sesión
      if (data.id?.ses) {
        const oldSessionId = this.sessionConfig.sessionId;
        this.sessionConfig.sessionId = data.id.ses;
        console.log(`ℹ️ [${this.serviceName}] Session ID actualizado: ${oldSessionId} → ${this.sessionConfig.sessionId}`);
      }

      // Procesar respuesta
      const processedResponse = this.processAIResponse(data.output);

      // Construir respuesta final
      const aiResponse: AIResponse = {
        ok: true,
        output: processedResponse.content,
        id: data.id ?? {
          ses: this.sessionConfig.sessionId || "",
          clt: "aura-game-client",
          corr: "",
        },
        input: payload.query,
        sizes: data.sizes ?? {
          completion_tokens: 0,
          prompt_tokens: 0,
          total_tokens: 0
        },
      };

      this.stats.responsesParsed++;
      console.log(`✅ [${this.serviceName}] Respuesta generada exitosamente`);

      return aiResponse;

    } catch (error) {
      this.stats.errorsCount++;
      console.error(`❌ [${this.serviceName}] Error generando respuesta`, error);

      return {
        ok: false,
        output: "",
        id: { ses: this.sessionConfig.sessionId || "", corr: "" },
        input: query,
        sizes: { completion_tokens: 0, prompt_tokens: 0, total_tokens: 0 },
      };
    }
  }

  /**
   * Versión silenciosa (sin speech automático)
   */
  public async generateResponseSilent(
    query: string,
    mode: GameMode,
    character?: string
  ): Promise<AIResponse> {
    const wasEnabled = this.isAutoSpeechEnabled();
    this.disableAutoSpeech();

    try {
      return await this.generateResponse(query, mode, character);
    } finally {
      if (wasEnabled) {
        this.enableAutoSpeech();
      }
    }
  }

  /**
   * Genera un nuevo personaje
   */
  public async generateCharacter(): Promise<string | null> {
    console.log(`🎭 [${this.serviceName}] Generando personaje`);

    try {
      this.stats.requestsCount++;

      const payload = payloadBuilder.buildCharacterPayload(this.sessionConfig.sessionId || undefined);
      const result = await requestHelper.makeRequest<any>("generate", payload);

      if (!result.success || !result.data?.output) {
        throw new Error("No se pudo generar el personaje");
      }

      const character = result.data.output.trim();
      console.log(`✅ [${this.serviceName}] Personaje generado: ${character}`);

      // Guardar personaje
      characterStorage.saveCurrentCharacter(character, this.sessionConfig.sessionId || undefined);

      // Narrar anuncio del personaje si está habilitado
      speechIntegration.narrateCharacterAnnouncement(character);

      this.stats.charactersGenerated++;
      return character;

    } catch (error) {
      this.stats.errorsCount++;
      console.error(`❌ [${this.serviceName}] Error generando personaje:`, error);
      return null;
    }
  }

  /**
   * Inicia un nuevo juego
   */
  public async startNewGame(
    mode: GameMode,
    character?: string
  ): Promise<AIResponse | null> {
    console.log(`🎮 [${this.serviceName}] Iniciando nuevo juego en modo: ${mode}`, { character });

    try {
      // Resetear sesión anterior si existe
      if (this.sessionConfig.sessionId) {
        await this.resetSession();
      }

      // Limpiar pistas y configurar nueva sesión
      this.clearAllClues();
      this.sessionConfig = {
        sessionId: null,
        mode,
        character: character || null,
        startTime: new Date(),
        lastActivity: new Date(),
        messageCount: 0,
        cluesGenerated: []
      };

      this.stats.sessionsCreated++;

      // Mensaje inicial según el modo
      const initialMessage = mode === "ia_vs_player"
        ? "¡Hola! Estoy listo para adivinar lo que sea. ¿Ya tienes un personaje en mente?"
        : "Piensa en alguien... y empezamos cuando quieras.";

      const response = await this.generateResponse(initialMessage, mode, character);

      if (response.ok) {
        console.log(`✅ [${this.serviceName}] Juego iniciado exitosamente`, {
          mode,
          sessionId: response.id.ses,
          character: character || "none",
        });
      }

      return response;

    } catch (error) {
      this.stats.errorsCount++;
      console.error(`❌ [${this.serviceName}] Error en startNewGame:`, error);
      return null;
    }
  }

  /**
   * Resetea la sesión actual
   */
  public async resetSession(): Promise<void> {
    console.log(`ℹ️ [${this.serviceName}] Reseteando sesión ${this.sessionConfig.sessionId}`);

    if (!this.sessionConfig.sessionId) {
      console.log(`ℹ️ [${this.serviceName}] No hay sesión que resetear`);
      return;
    }

    try {
      const payload = payloadBuilder.buildResetPayload(this.sessionConfig.sessionId);
      await requestHelper.simpleRequest("reset", payload);
      console.log(`✅ [${this.serviceName}] Sesión reseteada exitosamente`);
    } catch (error) {
      console.warn(`⚠️ [${this.serviceName}] Error reseteando sesión:`, error);
    } finally {
      this.clearSession();
    }
  }

  /**
   * Limpia la sesión local
   */
  public clearSession(): void {
    this.sessionConfig.sessionId = null;
    this.sessionConfig.mode = null;
    this.sessionConfig.character = null;
    console.log("🧹 [serviceName] Sesión local limpiada");
  }

  // ========== MÉTODOS PRIVADOS ==========

  /**
   * Procesa la respuesta de IA y maneja pistas automáticamente
   */
  private processAIResponse(output: string): { content: string; clue?: string } {
    const processed = responseParser.processAIResponse(output);

    if (processed.type === 'structured' && processed.structured) {
      // Guardar pista automáticamente si existe
      if (processed.structured.pista && processed.structured.pista.trim().length > 0) {
        const added = cluesStorage.addClue(
          processed.structured.pista,
          this.sessionConfig.sessionId || undefined
        );

        if (added) {
          this.stats.cluesGenerated++;
          this.sessionConfig.cluesGenerated.push({
            id: Date.now().toString(),
            text: processed.structured.pista,
            sessionId: this.sessionConfig.sessionId || undefined
          });
        }
      }

      // Narrar respuesta si está habilitado
      speechIntegration.narrateGameResponse(processed.structured.respuesta);

      return {
        content: processed.structured.respuesta,
        clue: processed.structured.pista
      };
    } else {
      // Respuesta de texto normal
      speechIntegration.narrateGameResponse(processed.text || output);

      return {
        content: processed.text || output
      };
    }
  }

  /**
   * Actualiza la actividad de la sesión
   */
  private updateSessionActivity(): void {
    this.sessionConfig.lastActivity = new Date();
    this.sessionConfig.messageCount++;
    this.stats.lastActivity = new Date();
  }

  // ========== MÉTODOS DE UTILIDAD ==========

  /**
   * Verifica el estado de salud del servicio
   */
  public async checkHealth(): Promise<boolean> {
    try {
      return await requestHelper.checkAPIHealth();
    } catch {
      return false;
    }
  }

  /**
   * Obtiene información detallada del servicio
   */
  public getServiceInfo() {
    return {
      serviceName: this.serviceName,
      session: this.sessionConfig,
      stats: this.stats,
      config: {
        api: API_CONFIG,
        speech: speechIntegration.getConfig()
      },
      storage: {
        cluesCount: this.getCluesCount(),
        currentCharacter: this.getStoredCharacter(),
        characterHistory: characterStorage.getStats()
      }
    };
  }
}

// ========== SINGLETON EXPORT ==========
export const aiService = AIService.getInstance();
