/**
 * ========================================================================
 * AI CONTENT SPEECH WRAPPER
 * ========================================================================
 *
 * Centralized wrapper that ensures all AI-generated content is automatically
 * narrated through the Azure TTS system while respecting mute/unmute controls.
 *
 * Features:
 * - Automatic speech for all AI responses
 * - Respects speech coordinator settings
 * - Prevents duplicate narration
 * - Configurable speech types for different content
 * ========================================================================
 */

import { speechCoordinator } from "./SpeechCoordinator";
import { aiService } from "./AIService";
import type { AIResponse } from "../models/services";
import type { GameMode } from "../contexts/EnygmaGameContext";

// ========== CONFIGURATION ==========
interface SpeechConfig {
  enabled: boolean;
  characterAnnouncement: boolean;
  responseNarration: boolean;
  welcomeMessages: boolean;
}

// ========== AI CONTENT SPEECH WRAPPER ==========
class AIContentSpeechWrapper {
  private static instance: AIContentSpeechWrapper;
  private serviceName = "aiContentSpeech";

  private config: SpeechConfig = {
    enabled: true,
    characterAnnouncement: true,
    responseNarration: true,
    welcomeMessages: true,
  };

  private constructor() {
    console.log(`ℹ️ [${this.serviceName}] 🎤 AI Content Speech Wrapper inicializado`);
  }

  public static getInstance(): AIContentSpeechWrapper {
    if (!AIContentSpeechWrapper.instance) {
      AIContentSpeechWrapper.instance = new AIContentSpeechWrapper();
    }
    return AIContentSpeechWrapper.instance;
  }

  // ========== CONFIGURATION METHODS ==========
  public enableAllSpeech(): void {
    this.config.enabled = true;
    aiService.enableAutoSpeech();
    console.log(`ℹ️ [${this.serviceName}] 🔊 Toda la narración AI habilitada`);
  }

  public disableAllSpeech(): void {
    this.config.enabled = false;
    aiService.disableAutoSpeech();
    console.log(`ℹ️ [${this.serviceName}] 🔇 Toda la narración AI deshabilitada`);
  }

  public enableCharacterAnnouncements(): void {
    this.config.characterAnnouncement = true;
    console.log(`ℹ️ [${this.serviceName}] 🎭 Anuncios de personajes habilitados`);
  }

  public disableCharacterAnnouncements(): void {
    this.config.characterAnnouncement = false;
    console.log(`ℹ️ [${this.serviceName}] 🎭 Anuncios de personajes deshabilitados`);
  }

  public enableResponseNarration(): void {
    this.config.responseNarration = true;
    console.log(`ℹ️ [${this.serviceName}] 💬 Narración de respuestas habilitada`);
  }

  public disableResponseNarration(): void {
    this.config.responseNarration = false;
    console.log(`ℹ️ [${this.serviceName}] 💬 Narración de respuestas deshabilitada`);
  }

  // ========== WRAPPER METHODS ==========
  /**
   * Generate AI response with automatic speech narration
   */
  public async generateResponseWithSpeech(
    query: string,
    mode: GameMode,
    character?: string
  ): Promise<AIResponse> {
    console.log(`ℹ️ [${this.serviceName}] 🤖 Generando respuesta con narración automática`);

    if (!this.config.enabled || !this.config.responseNarration) {
      // Use silent version if speech is disabled
      return aiService.generateResponseSilent(query, mode, character);
    }

    // Use normal version with automatic speech
    return aiService.generateResponse(query, mode, character);
  }

  /**
   * Generate character with automatic announcement
   */
  public async generateCharacterWithAnnouncement(): Promise<string | null> {
    console.log(`ℹ️ [${this.serviceName}] 🎭 Generando personaje con anuncio automático`);

    if (!this.config.enabled || !this.config.characterAnnouncement) {
      // Temporarily disable auto-speech for character generation
      const wasEnabled = aiService.isAutoSpeechEnabled();
      aiService.disableAutoSpeech();

      try {
        return await aiService.generateCharacter();
      } finally {
        if (wasEnabled) {
          aiService.enableAutoSpeech();
        }
      }
    }

    // Use normal version with automatic speech
    return aiService.generateCharacter();
  }

  /**
   * Start new game with appropriate speech settings
   */
  public async startNewGameWithSpeech(
    mode: GameMode,
    character?: string
  ): Promise<AIResponse | null> {
    console.log(`ℹ️ [${this.serviceName}] 🎮 Iniciando juego con configuración de speech`);

    if (!this.config.enabled || !this.config.welcomeMessages) {
      // Use silent version if welcome messages are disabled
      const wasEnabled = aiService.isAutoSpeechEnabled();
      aiService.disableAutoSpeech();

      try {
        return await aiService.startNewGame(mode, character);
      } finally {
        if (wasEnabled) {
          aiService.enableAutoSpeech();
        }
      }
    }

    // Use normal version with automatic speech
    return aiService.startNewGame(mode, character);
  }

  // ========== UTILITY METHODS ==========
  /**
   * Check if speech is currently enabled
   */
  public isSpeechEnabled(): boolean {
    return this.config.enabled && speechCoordinator.getState().isPlaying !== undefined;
  }

  /**
   * Get current configuration
   */
  public getConfig(): SpeechConfig {
    return { ...this.config };
  }

  /**
   * Reset all configurations to default
   */
  public resetToDefaults(): void {
    this.config = {
      enabled: true,
      characterAnnouncement: true,
      responseNarration: true,
      welcomeMessages: true,
    };
    aiService.enableAutoSpeech();
    console.log(`ℹ️ [${this.serviceName}] 🔄 Configuración restablecida a valores por defecto`);
  }
}

// ========== SINGLETON EXPORT ==========
export const aiContentSpeechWrapper = AIContentSpeechWrapper.getInstance();
