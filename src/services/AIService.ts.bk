import type { AIResponse, APIPayload } from "../models/services";
import type { GameMode } from "../models/game";
import { speechCoordinator } from "./SpeechCoordinator";

// ========== CONFIGURATION ==========
const API_CONFIG = {
  baseURL:
    import.meta.env.VITE_IA_API_URL || "https://dev.dl2discovery.org/llm/",
  apiKey:
    import.meta.env.VITE_IA_API_KEY || "b3df20aa-db3b-49ef-8d3b-4abfac8c1161",
  presets: {
    aura:
      import.meta.env.VITE_IA_PRESETID_IA_VS_PLAYER ||
      "mapp-Test akinator_claude37US",
    user:
      import.meta.env.VITE_IA_PRESETID_PLAYER_VS_IA ||
      "mapp-Test akinator_claude37Bi",
    genCharBot:
      import.meta.env.VITE_IA_PRESETID_GENCHARBOT || "mapp-gen-char-bot",
  },
};

export interface GameClue {
  id: string;
  text: string;
  sessionId?: string;
}

export interface CluesStorage {
  clues: GameClue[];
  lastUpdated: Date;
  sessionId?: string;
}

export interface AIGameResponse {
  respuesta: string;
  pista: string;
  acertado: boolean;
}

// Interfaz para el resultado del parsing
export interface ParseResult {
  success: boolean;
  data?: AIGameResponse;
  error?: string;
  rawOutput?: string;
}

/**
 * Carga las pistas guardadas desde localStorage
 */
function loadCluesFromStorage(): CluesStorage {
  try {
    const stored = localStorage.getItem("clues");
    if (!stored) {
      return { clues: [], lastUpdated: new Date() };
    }

    const parsed = JSON.parse(stored);

    // Convertir timestamps de string a Date si es necesario
    if (parsed.clues) {
      parsed.clues = parsed.clues.map((clue: any) => ({
        ...clue,
        timestamp: new Date(clue.timestamp)
      }));
    }

    parsed.lastUpdated = new Date(parsed.lastUpdated);

    return parsed;
  } catch (error) {
    console.warn("⚠️ [cluesStorage] Error cargando pistas desde localStorage:", error);
    return { clues: [], lastUpdated: new Date() };
  }
}

/**
 * Guarda las pistas en localStorage
 */
function saveCluesStorage(cluesStorage: CluesStorage): void {
  try {
    localStorage.setItem("clues", JSON.stringify(cluesStorage));
    console.log(`💾 [cluesStorage] Pistas guardadas (${cluesStorage.clues.length} total)`);
  } catch (error) {
    console.error("❌ [cluesStorage] Error guardando pistas:", error);
  }
}

/**
 * Agrega una nueva pista al almacenamiento
 */
function addClueToStorage(clueText: string, sessionId?: string): void {
  if (!clueText || clueText.trim().length === 0) {
    return; // No guardar pistas vacías
  }

  const cluesStorage = loadCluesFromStorage();

  // Verificar si ya existe una pista similar para evitar duplicados
  const existingClue = cluesStorage.clues.find(clue =>
    clue.text.toLowerCase().trim() === clueText.toLowerCase().trim()
  );

  if (existingClue) {
    console.log("ℹ️ [cluesStorage] Pista ya existe, no se duplica:", clueText);
    return;
  }

  const newClue: GameClue = {
    id: `clue-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
    text: clueText.trim(),
    sessionId,
  };

  cluesStorage.clues.push(newClue);
  cluesStorage.lastUpdated = new Date();

  // Si hay sessionId, actualizar el sessionId del storage
  if (sessionId) {
    cluesStorage.sessionId = sessionId;
  }

  saveCluesStorage(cluesStorage);

  console.log(`✅ [cluesStorage] Nueva pista agregada: "${clueText}"`);
}

/**
 * Limpia todas las pistas almacenadas
 */
function clearCluesStorage(): void {
  try {
    localStorage.removeItem("clues");
    console.log("🧹 [cluesStorage] Pistas limpiadas");
  } catch (error) {
    console.error("❌ [cluesStorage] Error limpiando pistas:", error);
  }
}

/**
 * Obtiene todas las pistas almacenadas
 */
function getAllClues(): GameClue[] {
  return loadCluesFromStorage().clues;
}

/**
 * Obtiene las pistas de la sesión actual
 */
function getCluesForSession(sessionId: string): GameClue[] {
  const cluesStorage = loadCluesFromStorage();
  return cluesStorage.clues.filter(clue => clue.sessionId === sessionId);
}

/**
 * Detecta si el output contiene JSON estructurado válido
 *
 * @param output - El output de la respuesta de la IA
 * @returns true si contiene JSON estructurado, false si es texto normal
 */
export function isStructuredResponse(output: string): boolean {
  if (!output || typeof output !== 'string') return false;

  const cleanOutput = output.trim();

  // Buscar bloques de código JSON
  if (cleanOutput.includes('```json') || cleanOutput.includes('```\n{')) {
    return true;
  }

  // Buscar JSON directo (que empiece y termine con llaves)
  if (cleanOutput.startsWith('{') && cleanOutput.endsWith('}')) {
    return true;
  }

  // Buscar patrones JSON dentro del texto
  const jsonPattern = /\{[\s\S]*"respuesta"[\s\S]*"pista"[\s\S]*"acertado"[\s\S]*\}/;
  return jsonPattern.test(cleanOutput);
}

/**
 * Extrae y parsea la respuesta JSON de la IA desde el output
 * que puede venir envuelto en bloques de código markdown
 *
 * @param output - El output de la respuesta de la IA
 * @returns ParseResult con los datos extraídos o error
 */
export function parseAIGameResponse(output: string): ParseResult {
  try {
    if (!output || typeof output !== 'string') {
      return {
        success: false,
        error: 'Output vacío o inválido',
        rawOutput: output
      };
    }

    // 🆕 Primero verificar si es una respuesta estructurada
    if (!isStructuredResponse(output)) {
      return {
        success: false,
        error: 'Respuesta no estructurada - es texto normal',
        rawOutput: output
      };
    }

    // Limpiar el output
    let cleanOutput = output.trim();

    // Extraer JSON de bloques de código markdown (```json ... ```)
    const jsonBlockRegex = /```(?:json)?\s*\n?([\s\S]*?)\n?```/i;
    const jsonBlockMatch = cleanOutput.match(jsonBlockRegex);

    if (jsonBlockMatch) {
      cleanOutput = jsonBlockMatch[1].trim();
    }

    // Si no hay bloques de código, buscar directamente el JSON
    // que puede estar entre llaves { ... }
    if (!jsonBlockMatch) {
      const directJsonRegex = /\{[\s\S]*\}/;
      const directJsonMatch = cleanOutput.match(directJsonRegex);

      if (directJsonMatch) {
        cleanOutput = directJsonMatch[0];
      }
    }

    // Intentar parsear el JSON
    const parsedData = JSON.parse(cleanOutput) as AIGameResponse;

    // Validar que contiene los campos esperados
    if (!parsedData || typeof parsedData !== 'object') {
      return {
        success: false,
        error: 'El JSON parseado no es un objeto válido',
        rawOutput: output
      };
    }

    // Validar campos requeridos
    const requiredFields = ['respuesta', 'pista', 'acertado'];
    const missingFields = requiredFields.filter(field => !(field in parsedData));

    if (missingFields.length > 0) {
      return {
        success: false,
        error: `Campos faltantes: ${missingFields.join(', ')}`,
        rawOutput: output
      };
    }

    // Normalizar y validar tipos
    const normalizedData: AIGameResponse = {
      respuesta: String(parsedData.respuesta || '').trim(),
      pista: String(parsedData.pista || '').trim(),
      acertado: Boolean(parsedData.acertado)
    };

    // Validar que respuesta no esté vacía
    if (!normalizedData.respuesta) {
      return {
        success: false,
        error: 'La respuesta no puede estar vacía',
        rawOutput: output
      };
    }

    return {
      success: true,
      data: normalizedData
    };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Error desconocido al parsear JSON',
      rawOutput: output
    };
  }
}

/**
 * Versión simplificada que solo retorna los datos o null
 *
 * @param output - El output de la respuesta de la IA
 * @returns AIGameResponse o null si hay error
 */
export function extractAIGameData(output: string): AIGameResponse | null {
  const result = parseAIGameResponse(output);
  return result.success ? result.data! : null;
}

/**
 * Función helper para logging con información detallada
 * Solo intenta parsear si detecta que es una respuesta estructurada
 *
 * @param output - El output original
 * @param serviceName - Nombre del servicio para logging
 */
export function parseAndLogAIResponse(output: string, serviceName: string = 'aiService'): AIGameResponse | null {
  // 🆕 Primero verificar si es una respuesta estructurada
  if (!isStructuredResponse(output)) {
    console.log(`ℹ️ [${serviceName}] Respuesta de texto normal (no JSON):`, output.substring(0, 100) + '...');
    return null;
  }

  const result = parseAIGameResponse(output);

  if (result.success) {
    console.log(`✅ [${serviceName}] JSON parseado correctamente:`, result.data);
    return result.data!;
  } else {
    console.error(`❌ [${serviceName}] Error parseando respuesta estructurada:`, {
      error: result.error,
      rawOutput: result.rawOutput?.substring(0, 200) + '...'
    });
    return null;
  }
}

/**
 * Función inteligente que maneja tanto respuestas estructuradas como texto normal
 *
 * @param output - El output de la IA
 * @param serviceName - Nombre del servicio para logging
 * @param sessionId - Session ID actual (opcional, para guardar pista)
 * @returns Objeto con el tipo de respuesta y los datos correspondientes
 */
export function handleAIResponse(
  output: string,
  serviceName: string = 'aiService',
  sessionId?: string,
): {
  type: 'structured' | 'text';
  structured?: AIGameResponse;
  text?: string;
} {
  if (isStructuredResponse(output)) {
    const structured = parseAndLogAIResponse(output, serviceName);
    console.log(`ℹ️ [${serviceName}] Respuesta estructurada detectada`, {
      structured: structured ? structured.respuesta : 'null',
      pista: structured ? structured.pista : 'null',
      acertado: structured ? structured.acertado : 'null'
    });

    if (structured) {
      console.log(`ℹ️ [${serviceName}] Respuesta estructurada detectada`, {
        structured: structured.respuesta,
        pista: structured.pista,
        acertado: structured.acertado
      });

      // 🆕 GUARDAR PISTA AUTOMÁTICAMENTE si existe
      if (structured.pista && structured.pista.trim().length > 0) {
        addClueToStorage(structured.pista, sessionId);
      }

      return {
        type: 'structured',
        structured: structured,
      };
    } else {
      // Si falla el parsing, usar el texto como fallback
      return {
        type: 'text',
        text: output
      };
    }
  } else {
    console.log(`ℹ️ [${serviceName}] Respuesta de texto normal`);
    return {
      type: 'text',
      text: output
    };
  }
}

// ========== AI SERVICE ==========
class AIService {
  private static instance: AIService;
  private sessionId: string | null = null;
  private serviceName = "ai";
  private autoSpeechEnabled = true;

  private constructor() {}

  public static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  // ========== GETTERS ==========
  public getSessionId(): string | null {
    return this.sessionId;
  }

  public isAutoSpeechEnabled(): boolean {
    return this.autoSpeechEnabled;
  }

  public getPresetForMode(mode: GameMode): string {
    switch (mode) {
      case "player_vs_ia":
        return API_CONFIG.presets.user;
      case "ia_vs_player":
        return API_CONFIG.presets.aura;
      default:
        return API_CONFIG.presets.user;
    }
  }

  /**
   * Obtiene el personaje guardado en localStorage
   * @returns El personaje guardado o null si no existe
   */
  public getStoredCharacter(): string | null {
    try {
      return localStorage.getItem("enygma_generated_character");
    } catch (error) {
      console.warn(
        `⚠️ [${this.serviceName}] No se pudo leer localStorage:`,
        error
      );
      return null;
    }
  }

  public getApiConfig() {
    return { ...API_CONFIG };
  }

  // ========== 🆕 MÉTODOS PARA GESTIÓN DE PISTAS ==========

  /**
   * Obtiene todas las pistas guardadas
   */
  public getAllClues(): GameClue[] {
    return getAllClues();
  }

  /**
   * Obtiene las pistas de la sesión actual
   */
  public getSessionClues(): GameClue[] {
    if (!this.sessionId) return [];
    return getCluesForSession(this.sessionId);
  }

  /**
   * Limpia todas las pistas almacenadas
   */
  public clearAllClues(): void {
    clearCluesStorage();
  }

  /**
   * Obtiene el número total de pistas almacenadas
   */
  public getCluesCount(): number {
    return getAllClues().length;
  }

  /**
   * Obtiene las últimas N pistas
   */
  public getRecentClues(limit: number = 5): GameClue[] {
    const allClues = getAllClues();
    return allClues
      .slice(0, limit);
  }

  // ========== AUTO-SPEECH CONTROL ==========
  public enableAutoSpeech(): void {
    this.autoSpeechEnabled = true;
    console.log(`ℹ️ [${this.serviceName}] 🔊 Auto-speech habilitado`);
  }

  public disableAutoSpeech(): void {
    this.autoSpeechEnabled = false;
    console.log(`ℹ️ [${this.serviceName}] 🔇 Auto-speech deshabilitado`);
  }

  /**
   * Temporarily disable auto-speech for a single operation
   * Useful when external systems will handle the speech
   */
  public async generateResponseSilent(
    query: string,
    mode: GameMode,
    character?: string
  ): Promise<AIResponse> {
    const wasEnabled = this.autoSpeechEnabled;
    this.disableAutoSpeech();
    try {
      return await this.generateResponse(query, mode, character);
    } finally {
      if (wasEnabled) {
        this.enableAutoSpeech();
      }
    }
  }

  /**
   * Automatic AI content narration
   * Only speaks if auto-speech is enabled and speech coordinator allows it
   */
  private async narrateAIContent(content: string): Promise<void> {
    if (!this.autoSpeechEnabled) {
      console.log(
        `ℹ️ [${this.serviceName}] 🔇 Auto-speech deshabilitado, omitiendo narración`
      );
      return;
    }

    try {
      console.log(
        `ℹ️ [${this.serviceName}] 🎤 Iniciando auto-narración de contenido AI`
      );
      await speechCoordinator.speak(content, "game_response");
      console.log(`✅ [${this.serviceName}] 🎤 Auto-narración completada`);
    } catch (error) {
      console.warn(`⚠️ [${this.serviceName}] Error en auto-narración:`, error);
    }
  }

  // ========== CORE METHODS ==========
  private async makeRequest<T>(
    endpoint: string,
    payload: APIPayload
  ): Promise<T> {
    const url = `${API_CONFIG.baseURL}${endpoint}`;
    console.log(`ℹ️ [${this.serviceName}] Making request to: ${url}`);

    try {
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-API-Key": API_CONFIG.apiKey,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data as T;
    } catch (error) {
      if (import.meta.env.MODE === "development") {
        // console.error(`🔴 AI API ERROR: ${error}`);
      }
      throw new Error("Error al conectar con la API de IA");
    }
  }

  private buildPayload(
    query: string,
    mode: GameMode,
    character?: string
  ): APIPayload {
    const preset = this.getPresetForMode(mode);
    let template = undefined;

    if (mode === "ia_vs_player" && character) {
      template = `[CONTEXTO: El personaje en el que debes pensar es "${character}"]

      Pregunta del usuario: {MSG}

      Responde en formato JSON según las instrucciones del sistema.`;
    }

    return {
      id: {
        ses: this.sessionId || undefined,
        clt: "aura-game-client",
        corr: `game-${Date.now()}`,
      },
      preset,
      query,
      prompt_params: template ? { template } : {},
      model_params: {
        max_tokens: 200,
      },
    };
  }

  // ========== PUBLIC METHODS ==========
  public async generateResponse(
    query: string,
    mode: GameMode,
    character?: string
  ): Promise<AIResponse> {
    console.log(
      `ℹ️ [${this.serviceName}] Generando respuesta para modo ${mode}`,
      {
        query: query.substring(0, 100),
        sessionId: this.sessionId,
      }
    );

    try {
      const payload = this.buildPayload(query, mode, character);
      console.log(`🔍 [${this.serviceName}] Payload construido`, {
        preset: payload.preset,
        character: character || "none",
      });

      const data = await this.makeRequest<any>("generate", payload);

      if (!data.ok) {
        throw new Error(data.message || "Error en la respuesta de la IA");
      }

      // Actualizar sessionId si es una nueva sesión
      if (data.id?.ses) {
        const oldSessionId = this.sessionId;
        this.sessionId = data.id.ses;
        console.log(
          `ℹ️ [${this.serviceName}] Session ID actualizado: ${oldSessionId} → ${this.sessionId}`
        );
      }

      // 🆕 MANEJAR RESPUESTA CON ALMACENAMIENTO AUTOMÁTICO DE PISTAS
      const aiResponse = handleAIResponse(
        data.output,
        this.serviceName,
        this.sessionId || undefined,
      );

      let output = "";
      if (aiResponse.type === 'structured' && aiResponse.structured) {
        output = aiResponse.structured.respuesta || "";
      } else {
        output = data.output || "";
      }

      // Auto-narrar el contenido
      this.narrateAIContent(output);

      console.log(`✅ [${this.serviceName}] Respuesta generada exitosamente`, {
        outputLength: output.length,
        sessionId: this.sessionId,
        cluesCount: this.getCluesCount()
      });

      return {
        ok: true,
        output: output,
        id: data.id ?? {
          ses: this.sessionId || "",
          clt: "aura-game-client",
          corr: "",
        },
        input: payload.query,
        sizes: data.sizes ?? {},
      };
    } catch (error) {
      console.error(
        `❌ [${this.serviceName}] Error generando respuesta`,
        error
      );
      return {
        ok: false,
        output: "",
        id: { ses: this.sessionId || "", corr: "" },
        input: query,
        sizes: { completion_tokens: 0, prompt_tokens: 0, total_tokens: 0 },
      };
    }
  }

  public async resetSession(): Promise<void> {
    console.log(`ℹ️ [${this.serviceName}] Reseteando sesión ${this.sessionId}`);

    if (!this.sessionId) {
      console.log(`ℹ️ [${this.serviceName}] No hay sesión que resetear`);
      return;
    }

    try {
      const payload = {
        id: {
          ses: this.sessionId,
          clt: "aura-game-client",
          corr: `reset-${Date.now()}`,
        },
        preset: "",
        query: "",
        prompt_params: {},
        model_params: {
          max_tokens: 1,
        },
      };

      await this.makeRequest("reset", payload);
      console.log(`✅ [${this.serviceName}] Sesión reseteada exitosamente`);
    } catch (error) {
      console.warn(`⚠️ [${this.serviceName}] Error reseteando sesión:`, error);
    } finally {
      this.sessionId = null;
    }
  }

  public async generateCharacter(): Promise<string | null> {
    console.log(`🎭 [${this.serviceName}] Generando personaje con GENCHARBOT`);

    try {
      const payload = {
        id: {
          ses: this.sessionId || undefined,
          clt: "aura-game-client",
          corr: `genchar-${Date.now()}`,
        },
        corrid: `genchar-${Date.now()}`,
        preset: API_CONFIG.presets.genCharBot,
        query: "Dime un personaje",
        prompt_params: {
          preamble: "",
        },
        model_params: {
          max_tokens: 100,
        },
      };

      console.log(`🔍 [${this.serviceName}] Generando personaje con preset:`, {
        preset: payload.preset,
      });

      const data = await this.makeRequest<any>("generate", payload);

      if (!data.ok || !data.output) {
        throw new Error("No se pudo generar el personaje");
      }

      const character = data.output.trim();
      console.log(`✅ [${this.serviceName}] Personaje generado: ${character}`);

      // Guardar en localStorage
      try {
        localStorage.setItem("enygma_generated_character", character);
        console.log(
          `💾 [${this.serviceName}] Personaje guardado en localStorage`
        );
      } catch (error) {
        console.warn(
          `⚠️ [${this.serviceName}] No se pudo guardar en localStorage:`,
          error
        );
      }

      // // 🎤 AUTO-NARRATE: Automatically announce the character
      // this.narrateAIContent(`He elegido el personaje: ${character}`).catch(error => {
      //   console.warn(`⚠️ [${this.serviceName}] Error en auto-narración de personaje:`, error);
      // });

      return character;
    } catch (error) {
      console.error(
        `❌ [${this.serviceName}] Error generando personaje:`,
        error
      );
      return null;
    }
  }

  public async startNewGame(
    mode: GameMode,
    character?: string
  ): Promise<AIResponse | null> {
    console.log(
      `🎮 [${this.serviceName}] Iniciando nuevo juego en modo: ${mode}`,
      { character }
    );

    try {
      // Resetear sesión anterior si existe
      if (this.sessionId) {
        console.log(
          `🔄 [${this.serviceName}] Reseteando sesión anterior: ${this.sessionId}`
        );
        await this.resetSession();
      }

      // 🆕 Limpiar pistas al inicio de nuevo juego
      this.clearAllClues();
      console.log(`🧹 [${this.serviceName}] Pistas limpiadas para nuevo juego`);

      // Mensaje específico según el modo
      const initialMessage =
        mode === "player_vs_ia"
          ? "Piensa en alguien... y empezamos cuando quieras."
          : "¡Hola! Estoy listo para adivinar lo que sea. ¿Ya tienes un personaje en mente?";

      const response = await this.generateResponse(
        initialMessage,
        mode,
        character
      );

      if (response.ok) {
        console.log(`✅ [${this.serviceName}] Juego iniciado exitosamente`, {
          mode,
          sessionId: response.id.ses,
          character: character || "none",
        });
      }

      return response;
    } catch (error) {
      // log.error(this.serviceName, "Error en startNewGame", error);
      return null;
    }
  }

  public clearSession(): void {
    this.sessionId = null;
    console.log("🧹 AI Service: Sesión local limpiada");
  }
}

// ========== SINGLETON EXPORT ==========
export const aiService = AIService.getInstance();
// ========== EXPORTS ADICIONALES PARA USO EXTERNO ==========
export {
  loadCluesFromStorage,
  saveCluesStorage,
  addClueToStorage,
  clearCluesStorage,
  getAllClues,
  getCluesForSession
};
