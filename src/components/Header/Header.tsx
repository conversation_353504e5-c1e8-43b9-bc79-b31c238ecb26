import { useSpeechOutput } from "../../contexts/SpeechProvider";
import IconGoBack from "../Buttons/GoBack/IconGoBack";
import IconMenu from "../Buttons/Menu/IconMenu";
import IconMicrophoneOn from "../Buttons/MicrophoneOn/IconMicrophoneOn";
import IconMicrophoneOff from "../Buttons/MicrophoneOff/IconMicrophoneOff";
import { ControlButton, SafeAreaHome } from "microapps";
import "./Header.scss";

interface HeaderProps {
  currentView: string;
  onBackToMain?: () => void;
  showBackButton?: boolean;
}

export const Header: React.FC<HeaderProps> = ({
  currentView,
  onBackToMain,
  showBackButton = false,
}) => {
  const {
    state: { audioState, isMusicPlaying, isSpeechPlaying },
    pauseMusic,
    resumeMusic,
    pauseSpeech,
    resumeSpeech,
  } = useSpeechOutput();

  const renderTitle = () => {
    switch (currentView) {
      case "play":
        return "Enygma";
      case "rules":
        return "Reglas";
      case "lives":
        return "Tus preguntas restantes";
      case "clues":
        return "Pistas descubiertas";
      default:
        return "";
    }
  };

  const handleMusicClick = () => {
    if (isMusicPlaying) {
      pauseMusic();
    } else {
      resumeMusic();
    }
  };

  const handleSpeechClick = () => {
    if (isSpeechPlaying) {
      pauseSpeech();
    } else {
      const audioManager = (window as any).audioManager;
      if (audioManager && audioManager.hasSpeechAudio()) {
        resumeSpeech();
      } else {
        console.warn("No hay audio de narración disponible para reproducir");
      }
    }
  };

  // Verificar si hay audio de speech disponible
  const hasSpeechAudio =
    (window as any).audioManager?.hasSpeechAudio?.() || false;

  return (
    <>
      <div className="header">
        <div className="header-left">
          {currentView === "main" && <IconMenu />}

          {showBackButton && currentView !== "main" && onBackToMain && (
            <div className="back-button" onClick={onBackToMain}>
              <IconGoBack />
            </div>
          )}
        </div>

        <div className="header-title">{renderTitle()}</div>

        <div className="header-right">
          <div>
            <ControlButton
              onClick={handleMusicClick}
              type="music"
              isActive={ !audioState.isMuted && isMusicPlaying}
              size="big"
            />

          </div>

          <div
            className={`sound-icon speech-control ${audioState.isMuted ? "muted" : ""} ${isSpeechPlaying ? "active" : ""} ${!hasSpeechAudio && !isSpeechPlaying ? "no-audio" : ""}`}
            onClick={handleSpeechClick}
            title={
              audioState.isMuted
                ? "Narración silenciada"
                : isSpeechPlaying
                  ? "Pausar narración"
                  : hasSpeechAudio
                    ? "Reanudar narración"
                    : "No hay narración disponible"
            }
          >
            {!isSpeechPlaying || audioState.isMuted ? (
              <IconMicrophoneOff />
            ) : (
              <IconMicrophoneOn />
            )}
          </div>

          <div className="home-icon">
            <SafeAreaHome isVisible={false} />
          </div>
        </div>
      </div>

    </>
  );
};
