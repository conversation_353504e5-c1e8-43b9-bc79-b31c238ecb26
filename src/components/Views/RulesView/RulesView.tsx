import { useState, useEffect } from "react";
import { IconButton, PrimaryButton } from "microapps";
import type { RulesViewData, ViewProps } from "../../../models/components";
import "./RulesView.scss";

export interface RulesViewProps extends ViewProps {
  // Props específicas de RulesView si las hay
}
interface GameMode {
  id: string;
  enabled: boolean;
  image: string;
  mode: "player_vs_ia" | "ia_vs_player";
  buttonText: string;
  description: string;
}

const RulesView: React.FC<RulesViewProps> = ({ isOpen, onClose, onStart }) => {
  // ========== STATE ==========
  const [gameModes, setGameModes] = useState<GameMode[]>([]);
  const [rulesData, setRulesData] = useState<RulesViewData | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [animationDirection, setAnimationDirection] = useState<"next" | "prev">(
    "next"
  );

  // ========== EFFECTS ==========
  useEffect(() => {
    const loadRulesData = async () => {
      try {
        const response = await fetch("/game-rules.json");
        if (!response.ok)
          throw new Error(`HTTP error! status: ${response.status}`);
        const data = await response.json();
        console.log("Rules loaded:", data);
        setRulesData(data);
      } catch (error) {}
    };

    const loadGameModes = async () => {
      try {
        const response = await fetch("/game-modes.json");
        if (!response.ok)
          throw new Error(`HTTP error! status: ${response.status}`);
        const data = await response.json();
        console.log("Game modes loaded:", data.gameModes);
        setGameModes(data.gameModes || []);
      } catch (error) {
        console.error("Error loading game modes:", error);
      }
    };

    if (isOpen) {
      loadRulesData();
      loadGameModes();
      setCurrentPage(0);
    }
  }, [isOpen]);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        onClose();
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [isOpen, onClose]);

  // ========== NAVIGATION HANDLERS ==========
  const handleNextPage = () => {
    if (!rulesData || currentPage >= rulesData.pages.length - 1 || isAnimating)
      return;

    setIsAnimating(true);
    setAnimationDirection("next");

    setTimeout(() => {
      setCurrentPage((prev) => prev + 1);
      setIsAnimating(false);
    }, 300);
  };

  const handlePrevPage = () => {
    if (currentPage <= 0 || isAnimating) return;

    setIsAnimating(true);
    setAnimationDirection("prev");

    setTimeout(() => {
      setCurrentPage((prev) => prev - 1);
      setIsAnimating(false);
    }, 300);
  };

  const handlePageIndicatorClick = (pageIndex: number) => {
    if (pageIndex === currentPage || isAnimating) return;

    setIsAnimating(true);
    setAnimationDirection(pageIndex > currentPage ? "next" : "prev");

    setTimeout(() => {
      setCurrentPage(pageIndex);
      setIsAnimating(false);
    }, 300);
  };

  // ========== RENDER GUARDS ==========
  if (!isOpen || !rulesData) return null;
  const currentPageData = rulesData.pages[currentPage];

  return (
    <div className="rules-modal">
      <div className="rules-content">
        <div className="rules-book">
          <IconButton
            ariaLabel="Página anterior"
            iconType="back"
            onClick={
              currentPage === 0 || isAnimating ? () => {} : handlePrevPage
            }
            size="small"
            state={currentPage === 0 || isAnimating ? "disabled" : "default"}
            className="rules-nav-arrow"
          />

          <div
            className={`rules-page-container ${isAnimating ? `page-flip-${animationDirection}` : ""}`}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="rules-card rules-image-card">
              {currentPageData.image && (
                <img
                  src={currentPageData.image}
                  alt={currentPageData.title}
                  onError={(e) => {
                    e.currentTarget.style.display = "none";
                  }}
                />
              )}
            </div>

            <div className="rules-card rules-text-card">
              <p className="rules-card-text body1">{currentPageData.title}</p>

              <div className="rules-page-indicators">
                {rulesData.pages.map((_, index) => (
                  <button
                    key={index}
                    className={`rules-page-indicator ${index === currentPage ? "active" : ""}`}
                    onClick={() => handlePageIndicatorClick(index)}
                    disabled={isAnimating}
                    aria-label={`Ir a página ${index + 1}`}
                  />
                ))}
              </div>
            </div>
          </div>

          <IconButton
            ariaLabel="Página siguiente"
            iconType="next"
            onClick={
              currentPage >= rulesData.pages.length - 1 || isAnimating
                ? () => {}
                : handleNextPage
            }
            size="small"
            state={
              currentPage >= rulesData.pages.length - 1 || isAnimating
                ? "disabled"
                : "default"
            }
            className="rules-nav-arrow"
          />
        </div>

        <div className="rules-action">
          {gameModes
            .filter((mode) => mode.enabled)
            .map((mode) => (
              <div key={mode.id} className="modes-card">
                <PrimaryButton
                  onClick={() => onStart(mode.mode)}
                  text={mode.buttonText}
                  className="primary-button rules-start-button"
                />
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};

export default RulesView;
