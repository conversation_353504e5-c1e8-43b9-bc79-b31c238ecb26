import { useState } from "react";
import { useGameOrchestrator } from "../../../contexts/GameOrchestratorContext";
import { PrimaryButton } from "microapps";
import './WelcomeScreen.scss'

interface WelcomeScreenProps {
  onGameReady: () => void;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ onGameReady }) => {
  const [isInitializing, setIsInitializing] = useState(false);
  const { initializeApp, isSilentMode } = useGameOrchestrator();

  const handleStartGame = async () => {
    setIsInitializing(true);

    try {
      console.log("WelcomeScreen: 🚀 Iniciando inicialización completa...");

      // Usar la inicialización completa del GameOrchestrator
      // Esto incluye configuración de voz, mensaje de bienvenida y música
      await initializeApp();

      console.log("✅ WelcomeScreen: Inicialización completada");

      // Mostrar advertencia si estamos en modo silencioso
      if (isSilentMode) {
        console.warn("⚠️ WelcomeScreen: Modo silencioso activado - sin voz");
      }

      // Notificar que todo está listo
      onGameReady();
    } catch (error) {
      console.error("❌ WelcomeScreen: Error en inicialización:", error);
      // Aún así, permitir continuar - la app puede funcionar sin voz
      onGameReady();
    } finally {
      setIsInitializing(false);
    }
  };
  

  return (
    <div className="welcome-screen-overlay">
      
      <div className="welcome-screen">
        <h1 className="welcome-title title1 bold">
          El velo del misterio se alza.
        </h1>

        <p className="welcome-body body1">
          ¿Estás listo para enfrentarte a Enygma y desvelar el personaje oculto en el que está pensando?
        </p>

        <PrimaryButton
            onClick={handleStartGame}
            isDisabled={isInitializing}
            isLoading={isInitializing}
            text="Empezar"
            backgroundColor="#88FFD5"
            textColor="#001428"
            borderRadius="8px"
            className="welcome-button primary-button"
        />

        <p className={`welcome-disclaimer ${isInitializing ? 'isInitializing' : ''} `}>
            Configurando la experiencia mágica...
        </p>
      </div>
    </div>
  );
};
